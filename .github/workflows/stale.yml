on:
  workflow_dispatch:
  schedule:
  - cron: '0 */4 * * *'

permissions:
  contents: read

jobs:
  prune_stale:
    permissions:
      issues: write  # for actions/stale to close stale issues
      pull-requests: write  # for actions/stale to close stale PRs
    name: <PERSON><PERSON><PERSON>
    runs-on: ubuntu-22.04
    # do not run it in forked repos
    if: github.repository == 'envoyproxy/ai-gateway'

    steps:
    - name: <PERSON><PERSON><PERSON>
      uses: actions/stale@5bef64f19d7facfb25b37b414482c7164d639639  # v9.1.0
      with:
        repo-token: ${{ secrets.GITHUB_TOKEN }}
        days-before-stale: 30
        days-before-close: -1
        stale-issue-message: >
          This issue has been automatically marked as stale because it has not had activity in the
          last 30 days.
        stale-pr-message: >
          This pull request has been automatically marked as stale because it has not had
          activity in the last 30 days.
          Please feel free to give a status update now, ping for review, when it's ready.
          Thank you for your contributions!
        stale-issue-label: 'stale'
        exempt-issue-labels: 'no stalebot,help wanted'
        stale-pr-label: 'stale'
        exempt-pr-labels: 'no stalebot'
        operations-per-run: 500
        ascending: true
